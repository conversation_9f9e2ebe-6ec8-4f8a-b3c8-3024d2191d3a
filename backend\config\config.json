{"development": {"username": "postgres", "password": "Reebo@2004", "database": "gemini_crm", "host": "localhost", "port": 5432, "dialect": "postgres", "logging": true}, "test": {"username": "postgres", "password": "Reebo@2004", "database": "gemini_crm_test", "host": "localhost", "port": 5432, "dialect": "postgres", "logging": false}, "production": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "logging": false, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}}}