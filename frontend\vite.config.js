import { defineConfig,  loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
// import { config } from 'dotenv'
// config()
// const env = loadEnv(process.env.NODE_ENV, process.cwd());

// Docker Api Url
//const API_URL = 'http://host.docker.internal:5000';

// Local Api Url
  const API_URL = 'http://localhost:5000';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  //allow host chigger-definite-nominally.ngrok-free.app
  server: {
    host: true,
    allowedHosts: ['http://localhost:5173', 'http://localhost:3000','chigger-definite-nominally.ngrok-free.app'
      ,'https://chigger-definite-nominally.ngrok-free.app'
    ],
    proxy: {
      '/api': {
        target:  API_URL,
        changeOrigin: true,
        secure: false
      },
      '/uploads': {
        target:  API_URL,
        changeOrigin: true,
        secure: false
      }
    }
  }
})
