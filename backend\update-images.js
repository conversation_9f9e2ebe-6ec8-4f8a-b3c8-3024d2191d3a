const { Sequelize } = require('sequelize');
require('dotenv').config();

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME || 'gemini_crm',
  process.env.DB_USER || 'postgres',
  process.env.DB_PASSWORD || 'Reebo@2004',
  {
    host: 'localhost', // Use localhost instead of postgres for direct connection
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    logging: console.log
  }
);

async function updateProductImages() {
  try {
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Update products with sample images
    const updates = [
      { id: 1, imagePath: 'sample-bag-1.svg' }, // Classic Leather Tote Bag
      { id: 2, imagePath: 'sample-bag-2.svg' }, // Designer Crossbody Purse
      { id: 4, imagePath: 'sample-bag-4.svg' }  // Canvas Shoulder Bag
    ];

    for (const update of updates) {
      const [affectedRows] = await sequelize.query(
        'UPDATE products SET "imagePath" = :imagePath WHERE id = :id',
        {
          replacements: { id: update.id, imagePath: update.imagePath },
          type: Sequelize.QueryTypes.UPDATE
        }
      );
      console.log(`Updated product ${update.id} with image ${update.imagePath} (${affectedRows} rows affected)`);
    }

    console.log('All products updated successfully!');
  } catch (error) {
    console.error('Error updating products:', error);
  } finally {
    await sequelize.close();
  }
}

updateProductImages();
